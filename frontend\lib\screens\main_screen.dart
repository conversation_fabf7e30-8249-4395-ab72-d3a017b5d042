import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/account_provider.dart';
import '../providers/task_provider.dart';
import '../providers/proxy_provider.dart' as proxy;
import '../widgets/task_list.dart';
import '../widgets/file_list.dart';
import '../widgets/stats_panel.dart';
import '../widgets/upload_form.dart';
import '../models/account.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  Future<void> _initializeData() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final accountProvider = Provider.of<AccountProvider>(context, listen: false);
    final taskProvider = Provider.of<TaskProvider>(context, listen: false);
    
    // 检查后端连接
    final isHealthy = await appProvider.checkHealth();
    if (!isHealthy) {
      _showError('后端服务未启动，请先启动Python后端服务');
      return;
    }
    
    // 加载数据
    final proxyProvider = Provider.of<proxy.ProxyProvider>(context, listen: false);
    await Future.wait([
      accountProvider.loadAccounts(),
      taskProvider.loadTasks(),
      appProvider.loadFolderHistory(),
      proxyProvider.loadProxyConfigs(),
    ]);
  }
  
  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }
  
  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Telegram自动上传工具'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.upload), text: '上传'),
            Tab(icon: Icon(Icons.list), text: '任务'),
            Tab(icon: Icon(Icons.settings), text: '设置'),
          ],
        ),
        actions: [
          Consumer<AppProvider>(
            builder: (context, appProvider, child) {
              return IconButton(
                icon: Icon(appProvider.isLoading ? Icons.sync : Icons.refresh),
                onPressed: appProvider.isLoading ? null : () async {
                  await appProvider.refresh();
                },
              );
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUploadTab(),
          _buildTasksTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }
  
  Widget _buildUploadTab() {
    return Row(
      children: [
        // 左侧上传表单
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 统计面板
                const StatsPanel(),
                const SizedBox(height: 16),
                
                // 上传表单
                Expanded(
                  child: SingleChildScrollView(
                    child: const UploadForm(),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // 分隔线
        const VerticalDivider(width: 1),
        
        // 右侧文件列表
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: const FileList(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildTasksTab() {
    return const TaskList();
  }
  
  Widget _buildSettingsTab() {
    return Consumer4<AppProvider, AccountProvider, TaskProvider, proxy.ProxyProvider>(
      builder: (context, appProvider, accountProvider, taskProvider, proxyProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 账号管理
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '账号管理',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 16),
                      
                      // 连接状态和操作按钮
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _showAddAccountDialog(),
                            icon: const Icon(Icons.add),
                            label: const Text('添加账号'),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: accountProvider.isLoading ? null : () => accountProvider.loadAccounts(),
                            icon: accountProvider.isLoading 
                              ? const SizedBox(
                                  width: 16, 
                                  height: 16, 
                                  child: CircularProgressIndicator(strokeWidth: 2)
                                )
                              : const Icon(Icons.refresh),
                            label: Text(accountProvider.isLoading ? '加载中...' : '刷新账号'),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 状态信息
                      if (accountProvider.error != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            border: Border.all(color: Colors.red.withOpacity(0.3)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.error, color: Colors.red),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  accountProvider.error!,
                                  style: const TextStyle(color: Colors.red),
                                ),
                              ),
                              TextButton(
                                onPressed: () => accountProvider.clearError(),
                                child: const Text('关闭'),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      
                      // 加载状态
                      if (accountProvider.isLoading && accountProvider.accounts.isEmpty) ...[
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      
                      // 账号列表
                      if (accountProvider.accounts.isNotEmpty) ...[
                        Text(
                          '已添加的账号 (${accountProvider.accounts.length}):',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        
                        ...accountProvider.accounts.map((account) {
                          final isLoggedIn = accountProvider.isAccountLoggedIn(account.id);
                          final message = accountProvider.getAccountMessage(account.id);
                          
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: isLoggedIn ? Colors.green : Colors.grey,
                                child: Icon(
                                  account.accountType == 'bot' ? Icons.smart_toy : Icons.person,
                                  color: Colors.white,
                                ),
                              ),
                              title: Row(
                                children: [
                                  Text(account.name),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: account.accountType == 'bot' ? Colors.blue : Colors.green,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      account.accountType == 'bot' ? 'Bot' : '用户',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('ID: ${account.id} • API ID: ${account.apiId}'),
                                  if (account.phone != null)
                                    Text('手机: ${account.phone}'),
                                  if (account.accountType == 'bot' && account.botToken != null)
                                    Text('Bot Token: ${account.botToken!.substring(0, 20)}...'),
                                  Text(
                                    message.isEmpty ? '未登录' : message,
                                    style: TextStyle(
                                      color: isLoggedIn ? Colors.green : Colors.orange,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    isLoggedIn ? Icons.check_circle : Icons.error,
                                    color: isLoggedIn ? Colors.green : Colors.red,
                                  ),
                                  const SizedBox(width: 8),
                                  if (!isLoggedIn)
                                    ElevatedButton(
                                      onPressed: () => _loginAccount(account),
                                      child: const Text('登录'),
                                    ),
                                  const SizedBox(width: 8),
                                  PopupMenuButton<String>(
                                    onSelected: (value) {
                                      switch (value) {
                                        case 'delete':
                                          _deleteAccount(account);
                                          break;
                                      }
                                    },
                                    itemBuilder: (context) => [
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Row(
                                          children: [
                                            Icon(Icons.delete, color: Colors.red),
                                            SizedBox(width: 8),
                                            Text('删除账号'),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      ] else if (!accountProvider.isLoading) ...[
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            border: Border.all(color: Colors.grey.withOpacity(0.3)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              const Icon(
                                Icons.account_circle_outlined,
                                size: 48,
                                color: Colors.grey,
                              ),
                              const SizedBox(height: 12),
                              const Text(
                                '还没有添加任何账号',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                '点击上方的"添加账号"按钮来添加你的第一个Telegram账号',
                                textAlign: TextAlign.center,
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 代理配置
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '代理配置',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          ElevatedButton.icon(
                            onPressed: () => _showAddProxyDialog(),
                            icon: const Icon(Icons.add),
                            label: const Text('添加代理'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      if (proxyProvider.proxyConfigs.isEmpty) ...[
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            '暂无代理配置，点击上方按钮添加',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                      ] else ...[
                        ...proxyProvider.proxyConfigs.map((proxy) {
                          return Card(
                            elevation: 1,
                            child: ListTile(
                              leading: Icon(
                                Icons.vpn_key,
                                color: proxy.enabled ? Colors.green : Colors.grey,
                              ),
                              title: Text(proxy.name),
                              subtitle: Text(
                                '${proxy.typeDisplayName} - ${proxy.displayAddress}',
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (proxy.isDefault)
                                    const Icon(
                                      Icons.star,
                                      color: Colors.amber,
                                      size: 16,
                                    ),
                                  Switch(
                                    value: proxy.enabled,
                                    onChanged: (value) {
                                      proxyProvider.toggleProxyEnabled(proxy.id, value);
                                    },
                                  ),
                                  PopupMenuButton<String>(
                                    onSelected: (value) {
                                      switch (value) {
                                        case 'edit':
                                          _showEditProxyDialog(proxy);
                                          break;
                                        case 'test':
                                          _testProxy(proxy.id);
                                          break;
                                        case 'default':
                                          proxyProvider.setDefaultProxy(proxy.id);
                                          break;
                                        case 'delete':
                                          _deleteProxy(proxy.id);
                                          break;
                                      }
                                    },
                                    itemBuilder: (context) => [
                                      const PopupMenuItem(
                                        value: 'edit',
                                        child: Text('编辑'),
                                      ),
                                      const PopupMenuItem(
                                        value: 'test',
                                        child: Text('测试'),
                                      ),
                                      if (!proxy.isDefault)
                                        const PopupMenuItem(
                                          value: 'default',
                                          child: Text('设为默认'),
                                        ),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Text('删除'),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 应用设置
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '应用设置',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 16),
                      
                      SwitchListTile(
                        title: const Text('自动刷新'),
                        subtitle: const Text('定期自动更新数据'),
                        value: appProvider.autoRefresh,
                        onChanged: (value) {
                          appProvider.setAutoRefresh(value);
                        },
                      ),
                      
                      ListTile(
                        title: const Text('刷新间隔'),
                        subtitle: Text('${appProvider.refreshInterval}秒'),
                        trailing: DropdownButton<int>(
                          value: appProvider.refreshInterval,
                          onChanged: (value) {
                            if (value != null) {
                              appProvider.setRefreshInterval(value);
                            }
                          },
                          items: const [
                            DropdownMenuItem(value: 1, child: Text('1秒')),
                            DropdownMenuItem(value: 2, child: Text('2秒')),
                            DropdownMenuItem(value: 5, child: Text('5秒')),
                            DropdownMenuItem(value: 10, child: Text('10秒')),
                          ],
                        ),
                      ),
                      
                      SwitchListTile(
                        title: const Text('显示通知'),
                        subtitle: const Text('任务完成时显示通知'),
                        value: appProvider.showNotifications,
                        onChanged: (value) {
                          appProvider.setShowNotifications(value);
                        },
                      ),

                      SwitchListTile(
                        title: const Text('默认跳过2FA'),
                        subtitle: const Text('登录时默认尝试跳过双因素认证'),
                        value: appProvider.defaultSkip2FA,
                        onChanged: (value) {
                          appProvider.setDefaultSkip2FA(value);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  Future<void> _showAddAccountDialog() async {
    final nameController = TextEditingController();
    final apiIdController = TextEditingController();
    final apiHashController = TextEditingController();
    final phoneController = TextEditingController();
    final botTokenController = TextEditingController();
    
    String accountType = 'user';
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('添加账号'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: '账号名称',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: apiIdController,
                      decoration: const InputDecoration(
                        labelText: 'API ID',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: apiHashController,
                      decoration: const InputDecoration(
                        labelText: 'API Hash',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // 账号类型选择
                    DropdownButtonFormField<String>(
                      value: accountType,
                      decoration: const InputDecoration(
                        labelText: '账号类型',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'user',
                          child: Text('用户账号'),
                        ),
                        DropdownMenuItem(
                          value: 'bot',
                          child: Text('Bot账号'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          accountType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // 根据账号类型显示不同的输入框
                    if (accountType == 'user') ...[
                      TextField(
                        controller: phoneController,
                        decoration: const InputDecoration(
                          labelText: '手机号码',
                          border: OutlineInputBorder(),
                          hintText: '+*************',
                        ),
                      ),
                    ] else if (accountType == 'bot') ...[
                      TextField(
                        controller: botTokenController,
                        decoration: const InputDecoration(
                          labelText: 'Bot Token',
                          border: OutlineInputBorder(),
                          hintText: '*********:ABCdefGhijklmnopQRSTuvwxyz',
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    );
    
    if (result == true) {
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);
      
      if (nameController.text.isEmpty || 
          apiIdController.text.isEmpty || 
          apiHashController.text.isEmpty) {
        _showError('请填写所有必要字段');
        return;
      }
      
      final apiId = int.tryParse(apiIdController.text);
      if (apiId == null) {
        _showError('API ID必须是数字');
        return;
      }
      
      // 验证账号类型相关字段
      if (accountType == 'bot') {
        if (botTokenController.text.isEmpty) {
          _showError('Bot账号需要提供Bot Token');
          return;
        }
      } else if (accountType == 'user') {
        if (phoneController.text.isEmpty) {
          _showError('用户账号需要提供手机号码');
          return;
        }
      }
      
      final success = await accountProvider.addAccount(
        name: nameController.text,
        apiId: apiId,
        apiHash: apiHashController.text,
        phone: accountType == 'user' ? phoneController.text : null,
        accountType: accountType,
        botToken: accountType == 'bot' ? botTokenController.text : null,
      );
      
      if (success) {
        _showSuccess('账号添加成功');
        // 强制刷新界面状态和账号列表
        if (mounted) {
          setState(() {});
          // 额外再刷新一次账号列表确保数据同步
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              accountProvider.loadAccounts();
            }
          });
        }
      } else {
        _showError(accountProvider.error ?? '添加账号失败');
      }
    }
  }
  
  Future<void> _loginAccount(Account account) async {
    final accountProvider = Provider.of<AccountProvider>(context, listen: false);
    
    // 首先尝试登录
    final success = await accountProvider.loginAccount(account);
    
    if (success) {
      _showSuccess('登录成功');
      return;
    }
    
    // 检查是否需要验证码
    final message = accountProvider.getAccountMessage(account.id);
    if (message.contains('验证码')) {
      _showVerificationCodeDialog(account.id);
    } else if (message.contains('密码')) {
      _showPasswordDialog(account.id);
    } else {
      _showError(accountProvider.error ?? '登录失败');
    }
  }
  
  Future<void> _showVerificationCodeDialog(int accountId) async {
    final codeController = TextEditingController();
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    bool skip2FA = appProvider.defaultSkip2FA; // 使用全局设置作为默认值

    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('输入验证码'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: codeController,
                    decoration: const InputDecoration(
                      labelText: '验证码',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: const Text('跳过2FA验证'),
                    subtitle: const Text('如果账号未启用2FA或想要跳过2FA验证'),
                    value: skip2FA,
                    onChanged: (value) {
                      setState(() {
                        skip2FA = value ?? false;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('验证'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result == true && codeController.text.isNotEmpty) {
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);

      final verifyResult = await accountProvider.verifyCode(accountId, codeController.text, skip2FA: skip2FA);

      if (verifyResult == 'success') {
        _showSuccess('验证成功');
      } else if (verifyResult == 'needs_password') {
        // 需要密码验证，显示密码对话框
        _showPasswordDialog(accountId);
      } else {
        _showError(accountProvider.error ?? '验证失败');
      }
    }
  }
  
  Future<void> _showPasswordDialog(int accountId) async {
    final passwordController = TextEditingController();
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('输入密码'),
          content: TextField(
            controller: passwordController,
            decoration: const InputDecoration(
              labelText: '密码',
              border: OutlineInputBorder(),
            ),
            obscureText: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('验证'),
            ),
          ],
        );
      },
    );
    
    if (result == true && passwordController.text.isNotEmpty) {
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);
      
      final success = await accountProvider.verifyPassword(accountId, passwordController.text);
      
      if (success) {
        _showSuccess('验证成功');
      } else {
        _showError(accountProvider.error ?? '验证失败');
      }
    }
  }

  // 代理配置相关方法
  Future<void> _showAddProxyDialog() async {
    final nameController = TextEditingController();
    final hostnameController = TextEditingController();
    final portController = TextEditingController();
    final usernameController = TextEditingController();
    final passwordController = TextEditingController();
    String selectedType = 'HTTP';
    bool enabled = false;
    bool isDefault = false;
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('添加代理配置'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: '代理名称',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        labelText: '代理类型',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'HTTP', child: Text('HTTP')),
                        DropdownMenuItem(value: 'SOCKS4', child: Text('SOCKS4')),
                        DropdownMenuItem(value: 'SOCKS5', child: Text('SOCKS5')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedType = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: hostnameController,
                      decoration: const InputDecoration(
                        labelText: '服务器地址',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: portController,
                      decoration: const InputDecoration(
                        labelText: '端口',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: usernameController,
                      decoration: const InputDecoration(
                        labelText: '用户名 (可选)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: passwordController,
                      decoration: const InputDecoration(
                        labelText: '密码 (可选)',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    CheckboxListTile(
                      title: const Text('启用代理'),
                      value: enabled,
                      onChanged: (value) {
                        setState(() {
                          enabled = value ?? false;
                        });
                      },
                    ),
                    CheckboxListTile(
                      title: const Text('设为默认代理'),
                      value: isDefault,
                      onChanged: (value) {
                        setState(() {
                          isDefault = value ?? false;
                          if (isDefault) enabled = true;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    );
    
    if (result == true) {
      final proxyProvider = Provider.of<proxy.ProxyProvider>(context, listen: false);
      
      if (nameController.text.isEmpty || 
          hostnameController.text.isEmpty || 
          portController.text.isEmpty) {
        _showError('请填写所有必要字段');
        return;
      }
      
      final port = int.tryParse(portController.text);
      if (port == null || port < 1 || port > 65535) {
        _showError('端口必须是1-65535之间的数字');
        return;
      }
      
      final success = await proxyProvider.addProxyConfig(
        name: nameController.text,
        proxyType: selectedType,
        hostname: hostnameController.text,
        port: port,
        username: usernameController.text.isEmpty ? null : usernameController.text,
        password: passwordController.text.isEmpty ? null : passwordController.text,
        enabled: enabled,
        isDefault: isDefault,
      );
      
      if (success) {
        _showSuccess('代理配置添加成功');
      } else {
        _showError(proxyProvider.error ?? '添加代理配置失败');
      }
    }
  }

  Future<void> _showEditProxyDialog(dynamic proxyConfig) async {
    // 编辑代理配置的对话框
    _showError('编辑功能开发中');
  }

  Future<void> _testProxy(int proxyId) async {
    final proxyProvider = Provider.of<proxy.ProxyProvider>(context, listen: false);
    
    try {
      final result = await proxyProvider.testProxyConfig(proxyId);
      if (result != null && result.success) {
        _showSuccess('代理测试成功，延迟: ${result.latency}ms');
      } else {
        _showError('代理测试失败');
      }
    } catch (e) {
      _showError('代理测试失败: $e');
    }
  }

  Future<void> _deleteProxy(int proxyId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: const Text('确定要删除这个代理配置吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
    
    if (confirmed == true) {
      final proxyProvider = Provider.of<proxy.ProxyProvider>(context, listen: false);
      final success = await proxyProvider.deleteProxyConfig(proxyId);
      
      if (success) {
        _showSuccess('代理配置已删除');
      } else {
        _showError(proxyProvider.error ?? '删除代理配置失败');
      }
    }
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除账号 "${account.name}" 吗？此操作不可逆。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);
      final success = await accountProvider.deleteAccount(account.id);

      if (success) {
        _showSuccess('账号已删除');
        if (mounted) {
          setState(() {});
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              accountProvider.loadAccounts();
            }
          });
        }
      } else {
        _showError(accountProvider.error ?? '删除账号失败');
      }
    }
  }
} 