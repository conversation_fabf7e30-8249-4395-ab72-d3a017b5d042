import asyncio
import os
import time
from typing import Dict, List, Optional, Callable
from pyrogram import Client, types, enums
from pyrogram.errors import (
    SessionPasswordNeeded, PhoneCodeExpired, PhoneCodeInvalid,
    PasswordHashInvalid, BadRequest, FloodWait, PhoneNumberInvalid,
    ApiIdInvalid, Unauthorized
)
from database import Database
import threading
from concurrent.futures import ThreadPoolExecutor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramClientManager:
    def __init__(self, db: Database):
        self.db = db
        self.clients: Dict[int, Client] = {}
        self.login_states: Dict[int, str] = {}  # account_id -> state
        self.login_data: Dict[int, Dict] = {}  # account_id -> login data
        self.upload_stats = {
            'total_speed': 0,
            'active_uploads': 0,
            'total_uploaded': 0,
            'total_size': 0
        }
        self.upload_callbacks = []
        self.task_callbacks = []
        self.speed_calculator = SpeedCalculator()
    
    async def create_client(self, account_name: str, account_id: int, api_id: int, api_hash: str, phone: str = None, session_string: str = None, proxy_config: dict = None, account_type: str = 'user', bot_token: str = None) -> Client:
        """创建Telegram客户端"""
        try:
            # 使用固定的session名称
            session_name = f"account_{account_name}"
            
            # 构建代理配置
            proxy = None
            if proxy_config and proxy_config.get('enabled'):
                proxy = {
                    'scheme': proxy_config['proxy_type'].upper(),
                    'hostname': proxy_config['hostname'],
                    'port': proxy_config['port']
                }
                if proxy_config.get('username'):
                    proxy['username'] = proxy_config['username']
                if proxy_config.get('password'):
                    proxy['password'] = proxy_config['password']
            
            # 检查是否已有客户端
            if account_id in self.clients:
                existing_client = self.clients[account_id]
                if existing_client.is_connected:
                    logger.info(f"账号 {account_id} 已有活跃连接")
                    return existing_client
                else:
                    # 清理旧的客户端
                    try:
                        await existing_client.stop()
                    except:
                        pass
                    del self.clients[account_id]
            
            # Bot Token 登录
            if account_type == 'bot' and bot_token:
                logger.info(f"使用Bot Token登录账号 {account_id}")
                client = Client(
                    name=session_name,
                    api_id=api_id,
                    api_hash=api_hash,
                    bot_token=bot_token,
                    proxy=proxy,
                    workdir="sessions"
                )
                
                try:
                    await client.start()
                    # 验证bot是否有效
                    me = await client.get_me()
                    self.clients[account_id] = client
                    logger.info(f"Bot Token登录成功: {me.first_name} (@{me.username})")
                    return client
                except Exception as e:
                    logger.error(f"Bot Token登录失败: {e}")
                    try:
                        await client.stop()
                    except:
                        pass
                    raise Exception(f"Bot Token登录失败: {str(e)}")
            
            # 用户账号登录
            if session_string:
                # 如果有session字符串，直接使用
                client = Client(
                    name=session_name,
                    session_string=session_string,
                    api_id=api_id,
                    api_hash=api_hash,
                    proxy=proxy,
                    workdir="sessions"
                )
                
                try:
                    await client.start()
                    # 验证session是否有效
                    await client.get_me()
                    self.clients[account_id] = client
                    logger.info(f"使用现有session成功登录账号 {account_id}")
                    return client
                except Exception as e:
                    logger.warning(f"现有session无效，需要重新登录: {e}")
                    try:
                        await client.stop()
                    except:
                        pass
                    # 清除无效的session
                    self.db.update_session_string(account_id, None)
            
            # 需要手机号码登录
            if not phone:
                raise Exception("需要提供手机号码")
            
            # 创建新的客户端进行登录
            client = Client(
                name=session_name,
                api_id=api_id,
                api_hash=api_hash,
                phone_number=phone,
                proxy=proxy,
                workdir="sessions"
            )
            
            # 初始化登录数据
            self.login_data[account_id] = {
                'client': client,
                'session_name': session_name,
                'phone': phone
            }
            
            # 连接并发送验证码
            await client.connect()
            sent_code = await client.send_code(phone)
            self.login_data[account_id]['phone_code_hash'] = sent_code.phone_code_hash
            self.login_states[account_id] = 'waiting_code'
            
            logger.info(f"验证码已发送到 {phone}")
            raise Exception("需要输入验证码")
            
        except (PhoneCodeInvalid, PhoneCodeExpired) as e:
            logger.info(f"需要验证码 (账号ID: {account_id}): {e}")
            raise e
        except SessionPasswordNeeded as e:
            logger.info(f"需要密码 (账号ID: {account_id}): {e}")
            raise e
        except Exception as e:
            error_msg = str(e)
            # 如果是正常的验证码请求，不清理登录数据
            if "需要输入验证码" in error_msg:
                logger.info(f"账号 {account_id} 需要验证码，保持登录状态")
                raise e
            
            logger.error(f"创建客户端失败 (账号ID: {account_id}): {e}")
            # 只有在真正失败时才清理登录数据
            if account_id in self.login_data:
                try:
                    client = self.login_data[account_id].get('client')
                    if client:
                        await client.stop()
                except:
                    pass
                del self.login_data[account_id]
            self.login_states.pop(account_id, None)
            raise e
    
    async def login_with_code(self, account_id: int, phone_code: str, skip_2fa: bool = False) -> bool:
        """使用验证码登录"""
        try:
            logger.info(f"开始验证账号 {account_id} 的验证码: {phone_code}, 跳过2FA: {skip_2fa}")

            if account_id not in self.login_data:
                logger.error(f"账号 {account_id} 没有登录数据，可能登录会话已过期")
                # 检查当前状态
                current_state = self.login_states.get(account_id)
                logger.error(f"当前登录状态: {current_state}")
                logger.error(f"可用的登录数据账号: {list(self.login_data.keys())}")
                return False

            current_state = self.login_states.get(account_id)
            if current_state != 'waiting_code':
                logger.error(f"账号 {account_id} 状态错误: {current_state}，期望状态: waiting_code")
                return False

            login_data = self.login_data[account_id]
            client = login_data['client']

            logger.info(f"验证账号 {account_id} 的验证码，手机号: {login_data['phone']}")

            try:
                await client.sign_in(
                    phone_number=login_data['phone'],
                    phone_code_hash=login_data['phone_code_hash'],
                    phone_code=phone_code
                )

                # 验证码正确，获取用户信息
                me = await client.get_me()
                self.clients[account_id] = client

                # 更新session字符串
                session_str = await client.export_session_string()
                self.db.update_session_string(account_id, session_str)

                # 清理登录数据
                del self.login_data[account_id]
                self.login_states.pop(account_id, None)

                logger.info(f"账号 {account_id} 验证码登录成功: {me.first_name}")
                return True

            except SessionPasswordNeeded:
                if skip_2fa:
                    # 如果设置了跳过2FA，则尝试使用空密码或默认密码
                    logger.info(f"账号 {account_id} 需要2FA密码，但设置了跳过2FA，尝试跳过")
                    try:
                        # 尝试使用空密码
                        await client.check_password("")

                        # 密码验证成功，获取用户信息
                        me = await client.get_me()
                        self.clients[account_id] = client

                        # 更新session字符串
                        session_str = await client.export_session_string()
                        self.db.update_session_string(account_id, session_str)

                        # 清理登录数据
                        del self.login_data[account_id]
                        self.login_states.pop(account_id, None)

                        logger.info(f"账号 {account_id} 跳过2FA登录成功: {me.first_name}")
                        return True

                    except Exception as pwd_e:
                        logger.warning(f"账号 {account_id} 跳过2FA失败: {pwd_e}")
                        # 如果跳过失败，仍然需要密码
                        self.login_states[account_id] = 'waiting_password'
                        logger.info(f"账号 {account_id} 跳过2FA失败，仍需要二步验证密码")
                        return False
                else:
                    # 需要二步验证密码
                    self.login_states[account_id] = 'waiting_password'
                    logger.info(f"账号 {account_id} 需要二步验证密码")
                    return False

        except (PhoneCodeInvalid, PhoneCodeExpired) as e:
            logger.error(f"验证码错误 (账号ID: {account_id}): {e}")
            # 不要清理登录数据，允许用户重新输入验证码
            return False
        except Exception as e:
            logger.error(f"验证码登录失败 (账号ID: {account_id}): {e}")
            return False
    
    async def login_with_password(self, account_id: int, password: str) -> bool:
        """使用密码登录（两步验证）"""
        try:
            if account_id not in self.login_data:
                # 检查是否已有活跃客户端需要密码
                if account_id in self.clients:
                    client = self.clients[account_id]
                    try:
                        await client.check_password(password)
                        
                        # 更新session字符串
                        session_str = await client.export_session_string()
                        self.db.update_session_string(account_id, session_str)
                        
                        logger.info(f"账号 {account_id} 密码验证成功")
                        return True
                    except Exception as e:
                        logger.error(f"密码验证失败: {e}")
                        return False
                
                logger.error(f"账号 {account_id} 没有登录数据")
                return False
            
            if self.login_states.get(account_id) != 'waiting_password':
                logger.error(f"账号 {account_id} 状态错误: {self.login_states.get(account_id)}")
                return False
            
            login_data = self.login_data[account_id]
            client = login_data['client']
            
            try:
                await client.check_password(password)
                
                # 密码正确，获取用户信息
                me = await client.get_me()
                self.clients[account_id] = client
                
                # 更新session字符串
                session_str = await client.export_session_string()
                self.db.update_session_string(account_id, session_str)
                
                # 清理登录数据
                del self.login_data[account_id]
                self.login_states.pop(account_id, None)
                
                logger.info(f"账号 {account_id} 密码登录成功: {me.first_name}")
                return True
                
            except PasswordHashInvalid:
                logger.error(f"账号 {account_id} 密码错误")
                return False
                
        except Exception as e:
            logger.error(f"密码登录失败 (账号ID: {account_id}): {e}")
            return False
    
    async def disconnect_client(self, account_id: int):
        """断开客户端连接"""
        try:
            # 清理活跃客户端
            if account_id in self.clients:
                client = self.clients[account_id]
                if client.is_connected:
                    await client.stop()
                del self.clients[account_id]
            
            # 清理登录数据
            if account_id in self.login_data:
                client = self.login_data[account_id].get('client')
                if client and client.is_connected:
                    await client.stop()
                del self.login_data[account_id]
            
            self.login_states.pop(account_id, None)
            logger.info(f"账号 {account_id} 连接已断开")
            
        except Exception as e:
            logger.error(f"断开连接失败 (账号ID: {account_id}): {e}")
    
    def get_login_state(self, account_id: int) -> Optional[str]:
        """获取登录状态"""
        return self.login_states.get(account_id)
    
    def cleanup_failed_login(self, account_id: int):
        """清理失败的登录尝试"""
        try:
            if account_id in self.login_data:
                client = self.login_data[account_id].get('client')
                if client:
                    # 尝试安全地停止客户端
                    try:
                        if hasattr(client, 'is_connected') and client.is_connected:
                            # 如果客户端仍然连接，尝试断开
                            loop = None
                            try:
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    # 如果事件循环正在运行，创建一个任务
                                    asyncio.create_task(client.stop())
                                else:
                                    # 如果事件循环未运行，使用run_until_complete
                                    loop.run_until_complete(client.stop())
                            except RuntimeError:
                                # 如果没有事件循环，忽略停止操作
                                logger.warning(f"无法停止客户端 {account_id}：没有活跃的事件循环")
                                pass
                    except Exception as stop_e:
                        logger.warning(f"停止客户端 {account_id} 时出错: {stop_e}")
                del self.login_data[account_id]
            self.login_states.pop(account_id, None)
            logger.info(f"已清理账号 {account_id} 的登录数据")
        except Exception as e:
            logger.error(f"清理登录数据失败: {e}")
    
    async def resend_code(self, account_id: int) -> bool:
        """重新发送验证码"""
        try:
            if account_id not in self.login_data:
                logger.error(f"账号 {account_id} 没有登录数据，无法重新发送验证码")
                return False
            
            login_data = self.login_data[account_id]
            client = login_data['client']
            phone = login_data['phone']
            
            logger.info(f"重新发送验证码到 {phone}")
            
            # 重新发送验证码
            sent_code = await client.send_code(phone)
            self.login_data[account_id]['phone_code_hash'] = sent_code.phone_code_hash
            self.login_states[account_id] = 'waiting_code'
            
            logger.info(f"验证码已重新发送到 {phone}")
            return True
            
        except Exception as e:
            logger.error(f"重新发送验证码失败 (账号ID: {account_id}): {e}")
            return False

    async def get_dialogs(self, account_id: int) -> List[Dict]:
        """获取对话列表"""
        try:
            client = self.clients.get(account_id)
            if not client or not client.is_connected:
                logger.error(f"账号 {account_id} 客户端未连接")
                return []
            
            # 检查账号类型
            accounts = self.db.get_accounts()
            account = next((acc for acc in accounts if acc['id'] == account_id), None)
            
            if not account:
                logger.error(f"账号 {account_id} 不存在")
                return []
            
            # 如果是机器人账号，使用不同的方法
            if account.get('account_type') == 'bot':
                logger.info(f"账号 {account_id} 是机器人账号，无法获取对话列表")
                # 机器人账号无法使用 get_dialogs，返回提示信息
                return [
                    {
                        'id': 'bot_info',
                        'title': '📝 Bot账号使用说明',
                        'type': 'info',
                        'username': None,
                        'is_info': True,
                        'description': '机器人账号无法获取对话列表，请手动输入聊天ID'
                    }
                ]
            
            # 用户账号正常获取对话列表
            dialogs = []
            async for dialog in client.get_dialogs():
                chat_type = "private"
                if dialog.chat.type == enums.ChatType.GROUP:
                    chat_type = "group"
                elif dialog.chat.type == enums.ChatType.SUPERGROUP:
                    chat_type = "supergroup"
                elif dialog.chat.type == enums.ChatType.CHANNEL:
                    chat_type = "channel"
                
                dialogs.append({
                    'id': str(dialog.chat.id),
                    'title': dialog.chat.title or f"{dialog.chat.first_name or ''} {dialog.chat.last_name or ''}".strip(),
                    'type': chat_type,
                    'username': dialog.chat.username
                })
            
            return dialogs
            
        except Exception as e:
            logger.error(f"获取对话列表失败 (账号ID: {account_id}): {e}")
            # 检查是否是BOT_METHOD_INVALID错误
            if "BOT_METHOD_INVALID" in str(e) or "method cannot be used by bots" in str(e):
                logger.info(f"账号 {account_id} 是机器人账号，无法获取对话列表")
                return [
                    {
                        'id': 'bot_info',
                        'title': '📝 Bot账号使用说明',
                        'type': 'info',
                        'username': None,
                        'is_info': True,
                        'description': '机器人账号无法获取对话列表，请手动输入聊天ID'
                    }
                ]
            return []
    
    def parse_filename(self, filename: str) -> tuple:
        """解析文件名，返回(A, B)"""
        name_without_ext = os.path.splitext(filename)[0]
        # 找到第一个空格的位置
        return name_without_ext
        # space_index = name_without_ext.find(' ')
        # if space_index != -1:
        #     a_part = name_without_ext[:space_index]
        #     b_part = name_without_ext[space_index + 1:]
        #     return a_part, b_part
        # else:
        #     return name_without_ext, ""
    
    def generate_caption(self, filename: str) -> str:
        """生成文件描述"""
        return self.parse_filename(filename)
        # a_part, b_part = self.parse_filename(filename)
        # if b_part:
        #     return f"#{a_part} {b_part}"
        # else:
        #     return f"#{a_part}"
    
    async def upload_file(self, account_id: int, file_path: str, chat_id: str, task_id: int, file_record_id: int, progress_callback: Optional[Callable] = None):
        """上传单个文件"""
        try:
            client = self.clients.get(account_id)
            if not client:
                raise Exception("客户端不存在")
            
            filename = os.path.basename(file_path)
            caption = self.generate_caption(filename)
            
            # 文件大小
            file_size = os.path.getsize(file_path)
            
            # 获取文件扩展名并确定类型
            _, ext = os.path.splitext(file_path)
            ext_lower = ext.lower()
            
            # 进度回调函数
            def progress_handler(current, total):
                if progress_callback:
                    progress = (current / total) * 100
                    progress_callback(file_record_id, progress)
                    
                    # 更新速度统计
                    self.speed_calculator.update_progress(file_record_id, current, total)
            
            # 开始上传
            self.upload_stats['active_uploads'] += 1
            start_time = time.time()
            
            # 根据文件类型选择发送方式
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', 
                               '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.rm', '.rmvb', '.asf'}
            audio_extensions = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', 
                               '.opus', '.aiff', '.ape', '.dts', '.ac3', '.amr'}
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', 
                               '.svg', '.ico', '.heic', '.heif'}
            
            if ext_lower in video_extensions:
                # 视频文件
                logger.info(f"上传视频文件: {filename}")
                await client.send_video(
                    chat_id=chat_id,
                    video=file_path,
                    caption=caption,
                    progress=progress_handler
                )
            elif ext_lower in audio_extensions:
                # 音频文件
                logger.info(f"上传音频文件: {filename}")
                await client.send_audio(
                    chat_id=chat_id,
                    audio=file_path,
                    caption=caption,
                    progress=progress_handler
                )
            elif ext_lower in image_extensions:
                # 图片文件
                logger.info(f"上传图片文件: {filename}")
                await client.send_photo(
                    chat_id=chat_id,
                    photo=file_path,
                    caption=caption,
                    progress=progress_handler
                )
            else:
                # 其他文件（文档、压缩包等）
                logger.info(f"上传文档文件: {filename}")
                await client.send_document(
                    chat_id=chat_id,
                    document=file_path,
                    caption=caption,
                    progress=progress_handler
                )
            
            # 上传完成
            upload_time = time.time() - start_time
            self.upload_stats['active_uploads'] -= 1
            self.upload_stats['total_uploaded'] += file_size
            
            # 更新数据库
            self.db.update_file_upload_progress(file_record_id, 100.0, 'completed')
            
            # 触发回调
            for callback in self.upload_callbacks:
                callback({
                    'type': 'file_completed',
                    'file_id': file_record_id,
                    'file_path': file_path,
                    'upload_time': upload_time
                })
            
            logger.info(f"文件上传成功: {filename}, 耗时: {upload_time:.2f}秒")
            return True
            
        except Exception as e:
            logger.error(f"上传文件失败 {file_path}: {e}")
            self.upload_stats['active_uploads'] -= 1
            self.db.update_file_upload_progress(file_record_id, 0, 'failed', str(e))
            
            # 触发回调
            for callback in self.upload_callbacks:
                callback({
                    'type': 'file_failed',
                    'file_id': file_record_id,
                    'file_path': file_path,
                    'error': str(e)
                })
            
            return False
    
    async def upload_folder(self, account_id: int, folder_path: str, chat_id: str, task_id: int):
        """上传文件夹中的所有文件"""
        try:
            # 使用FileManager扫描文件夹
            from file_manager import FileManager
            file_manager = FileManager()
            
            # 扫描文件夹获取所有支持的文件
            scanned_files = file_manager.scan_folder(folder_path)
            
            # 转换为上传文件列表格式
            files_to_upload = []
            for file_info in scanned_files:
                files_to_upload.append({
                    'path': file_info['path'],
                    'name': file_info['name'],
                    'size': file_info['size']
                })
            
            logger.info(f"准备上传文件夹: {folder_path}, 文件数量: {len(files_to_upload)}")
            
            # 更新任务状态
            self.db.update_task_status(task_id, 'running', len(files_to_upload))
            
            # 添加文件记录到数据库
            file_records = []
            for file_info in files_to_upload:
                file_id = self.db.add_file_upload(
                    task_id, 
                    file_info['path'], 
                    file_info['name'], 
                    file_info['size']
                )
                file_records.append({
                    'id': file_id,
                    'path': file_info['path'],
                    'name': file_info['name'],
                    'size': file_info['size']
                })
            
            # 更新总大小统计
            total_size = sum(f['size'] for f in file_records)
            self.upload_stats['total_size'] += total_size
            
            # 触发任务开始回调
            for callback in self.task_callbacks:
                callback({
                    'type': 'task_started',
                    'task_id': task_id,
                    'total_files': len(files_to_upload),
                    'total_size': total_size
                })
            
            # 上传文件
            completed_files = 0
            failed_files = 0
            
            for file_record in file_records:
                try:
                    # 进度回调
                    def progress_callback(file_id, progress):
                        self.db.update_file_upload_progress(file_id, progress)
                        
                        # 触发进度回调
                        for callback in self.upload_callbacks:
                            callback({
                                'type': 'file_progress',
                                'file_id': file_id,
                                'progress': progress
                            })
                    
                    success = await self.upload_file(
                        account_id, 
                        file_record['path'], 
                        chat_id, 
                        task_id, 
                        file_record['id'],
                        progress_callback
                    )
                    
                    if success:
                        completed_files += 1
                    else:
                        failed_files += 1
                    
                    # 更新任务统计
                    self.db.update_task_status(task_id, 'running', len(files_to_upload), completed_files, failed_files)
                    
                except Exception as e:
                    logger.error(f"上传文件出错 {file_record['path']}: {e}")
                    failed_files += 1
                    self.db.update_task_status(task_id, 'running', len(files_to_upload), completed_files, failed_files)
            
            # 完成任务
            final_status = 'completed' if failed_files == 0 else 'completed_with_errors'
            self.db.update_task_status(task_id, final_status, len(files_to_upload), completed_files, failed_files)
            
            # 触发任务完成回调
            for callback in self.task_callbacks:
                callback({
                    'type': 'task_completed',
                    'task_id': task_id,
                    'total_files': len(files_to_upload),
                    'completed_files': completed_files,
                    'failed_files': failed_files,
                    'status': final_status
                })
            
            logger.info(f"文件夹上传完成: {folder_path}, 成功: {completed_files}, 失败: {failed_files}")
            return True
            
        except Exception as e:
            logger.error(f"上传文件夹失败: {e}")
            self.db.update_task_status(task_id, 'failed', 0, 0, 0)
            
            # 触发任务失败回调
            for callback in self.task_callbacks:
                callback({
                    'type': 'task_failed',
                    'task_id': task_id,
                    'error': str(e)
                })
            
            return False
    
    def get_upload_stats(self) -> Dict:
        """获取上传统计信息"""
        return {
            **self.upload_stats,
            'current_speed': self.speed_calculator.get_current_speed()
        }
    
    def add_upload_callback(self, callback: Callable):
        """添加上传回调"""
        self.upload_callbacks.append(callback)
    
    def add_task_callback(self, callback: Callable):
        """添加任务回调"""
        self.task_callbacks.append(callback)
    
    async def disconnect_all_clients(self):
        """断开所有客户端连接"""
        for account_id in list(self.clients.keys()):
            await self.disconnect_client(account_id)

    async def resume_upload_task(self, task_id: int):
        """恢复上传任务"""
        try:
            # 获取任务信息
            task = self.db.get_task_by_id(task_id)
            if not task:
                raise Exception(f"任务 {task_id} 不存在")
            
            # 检查任务状态
            if task['status'] not in ['pending', 'paused', 'failed']:
                raise Exception(f"任务 {task_id} 状态不支持恢复: {task['status']}")
            
            # 获取已完成的文件列表
            completed_files = set()
            task_files = self.db.get_task_files(task_id)
            for file_info in task_files:
                if file_info['status'] == 'completed':
                    completed_files.add(file_info['file_path'])
            
            # 重新扫描文件夹，跳过已完成的文件
            from file_manager import FileManager
            file_manager = FileManager()
            
            # 扫描文件夹获取所有支持的文件
            scanned_files = file_manager.scan_folder(task['folder_path'])
            
            files_to_upload = []
            for file_info in scanned_files:
                if file_info['path'] not in completed_files:
                    files_to_upload.append({
                        'path': file_info['path'],
                        'name': file_info['name'],
                        'size': file_info['size']
                    })
            
            # 更新任务状态为运行中
            self.db.update_task_status(task_id, 'running')
            
            # 继续上传未完成的文件
            account_id = task['account_id']
            chat_id = task['target_chat_id']
            
            completed_count = task['completed_files']
            failed_count = task['failed_files']
            
            for file_info in files_to_upload:
                try:
                    # 检查是否已有记录
                    existing_file = None
                    for tf in task_files:
                        if tf['file_path'] == file_info['path']:
                            existing_file = tf
                            break
                    
                    if not existing_file:
                        # 添加新文件记录
                        file_id = self.db.add_file_upload(
                            task_id,
                            file_info['path'],
                            file_info['name'],
                            file_info['size']
                        )
                    else:
                        file_id = existing_file['id']
                        # 重置文件状态
                        self.db.update_file_upload_progress(file_id, 0, 'pending')
                    
                    # 进度回调
                    def progress_callback(file_id, progress):
                        self.db.update_file_upload_progress(file_id, progress)
                    
                    success = await self.upload_file(
                        account_id,
                        file_info['path'],
                        chat_id,
                        task_id,
                        file_id,
                        progress_callback
                    )
                    
                    if success:
                        completed_count += 1
                    else:
                        failed_count += 1
                    
                    # 更新任务统计
                    self.db.update_task_status(
                        task_id, 'running', 
                        task['total_files'], completed_count, failed_count
                    )
                    
                except Exception as e:
                    logger.error(f"恢复上传文件出错 {file_info['path']}: {e}")
                    failed_count += 1
                    self.db.update_task_status(
                        task_id, 'running', 
                        task['total_files'], completed_count, failed_count
                    )
            
            # 完成任务
            final_status = 'completed' if failed_count == 0 else 'completed_with_errors'
            self.db.update_task_status(task_id, final_status, task['total_files'], completed_count, failed_count)
            
            logger.info(f"任务 {task_id} 恢复完成")
            return True
            
        except Exception as e:
            logger.error(f"恢复上传任务失败: {e}")
            self.db.update_task_status(task_id, 'failed')
            return False

    def recover_incomplete_tasks(self):
        """在应用启动时恢复未完成的任务"""
        try:
            incomplete_tasks = self.db.get_incomplete_tasks()
            logger.info(f"发现 {len(incomplete_tasks)} 个未完成的任务")
            
            for task in incomplete_tasks:
                # 检查账号是否可用
                if task['account_id'] not in self.clients:
                    logger.warning(f"任务 {task['id']} 的账号 {task['account_id']} 未登录，跳过恢复")
                    continue
                
                client = self.clients[task['account_id']]
                if not client.is_connected:
                    logger.warning(f"任务 {task['id']} 的账号 {task['account_id']} 未连接，跳过恢复")
                    continue
                
                # 在后台恢复任务
                def restore_task(task_id=task['id']):
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.resume_upload_task(task_id))
                        loop.close()
                    except Exception as e:
                        logger.error(f"恢复任务 {task_id} 失败: {e}")
                
                import threading
                thread = threading.Thread(target=restore_task)
                thread.daemon = True
                thread.start()
                
        except Exception as e:
            logger.error(f"恢复未完成任务失败: {e}")

    async def auto_login_all_accounts(self):
        """在应用启动时自动登录所有有session的账号"""
        try:
            accounts = self.db.get_accounts()
            logger.info(f"开始自动登录，发现 {len(accounts)} 个账号")
            
            for account in accounts:
                account_id = account['id']
                
                # 跳过已经登录的账号
                if account_id in self.clients:
                    client = self.clients[account_id]
                    if client.is_connected:
                        logger.info(f"账号 {account_id} ({account['name']}) 已经登录，跳过")
                        continue
                
                # 只自动登录有session_string或bot_token的账号
                has_session = account.get('session_string') is not None
                is_bot = account.get('account_type') == 'bot' and account.get('bot_token') is not None
                
                if not has_session and not is_bot:
                    logger.info(f"账号 {account_id} ({account['name']}) 没有保存的session，跳过自动登录")
                    continue
                
                try:
                    # 获取代理配置
                    proxy_config = self.db.get_default_proxy_config()
                    
                    await self.create_client(
                        account_name=account['name'],
                        account_id=account_id,
                        api_id=account['api_id'],
                        api_hash=account['api_hash'],
                        phone=account.get('phone'),
                        session_string=account.get('session_string'),
                        proxy_config=proxy_config,
                        account_type=account.get('account_type', 'user'),
                        bot_token=account.get('bot_token')
                    )
                    
                    logger.info(f"账号 {account_id} ({account['name']}) 自动登录成功")
                    
                except Exception as e:
                    error_msg = str(e)
                    # 如果是需要验证码或密码的错误，不记录为错误
                    if any(keyword in error_msg for keyword in ['验证码', 'phone_code', '密码', 'password']):
                        logger.info(f"账号 {account_id} ({account['name']}) 需要手动验证，跳过自动登录")
                    else:
                        logger.warning(f"账号 {account_id} ({account['name']}) 自动登录失败: {e}")
            
            # 记录最终状态
            logged_in_count = len(self.clients)
            logger.info(f"自动登录完成，成功登录 {logged_in_count} 个账号")
            
        except Exception as e:
            logger.error(f"自动登录过程失败: {e}")


class SpeedCalculator:
    def __init__(self):
        self.file_progress = {}
        self.speed_history = []
        self.last_update = time.time()
    
    def update_progress(self, file_id: int, current: int, total: int):
        """更新文件进度"""
        now = time.time()
        
        if file_id in self.file_progress:
            old_current = self.file_progress[file_id]['current']
            time_diff = now - self.file_progress[file_id]['timestamp']
            
            if time_diff > 0:
                speed = (current - old_current) / time_diff
                self.speed_history.append({
                    'speed': speed,
                    'timestamp': now
                })
                
                # 只保留最近10秒的数据
                self.speed_history = [
                    entry for entry in self.speed_history
                    if now - entry['timestamp'] <= 10
                ]
        
        self.file_progress[file_id] = {
            'current': current,
            'total': total,
            'timestamp': now
        }
    
    def get_current_speed(self) -> float:
        """获取当前速度（字节/秒）"""
        if not self.speed_history:
            return 0
        
        # 计算平均速度
        total_speed = sum(entry['speed'] for entry in self.speed_history)
        return total_speed / len(self.speed_history) if self.speed_history else 0
    
    def format_speed(self, speed: float) -> str:
        """格式化速度显示"""
        if speed < 1024:
            return f"{speed:.1f} B/s"
        elif speed < 1024 * 1024:
            return f"{speed/1024:.1f} KB/s"
        elif speed < 1024 * 1024 * 1024:
            return f"{speed/(1024*1024):.1f} MB/s"
        else:
            return f"{speed/(1024*1024*1024):.1f} GB/s" 