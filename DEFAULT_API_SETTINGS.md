# 默认API设置功能

## 功能概述

新增了默认API ID和API Hash设置功能，用户可以在设置中预先配置这些信息，添加新账号时会自动填入，避免重复输入。

## 功能特性

### 1. 默认API设置
- 在应用设置中添加"默认API设置"部分
- 可以设置默认的API ID和API Hash
- 设置后会持久保存在本地

### 2. 自动填入功能
- 添加新账号时，API ID和API Hash字段会自动填入默认值
- 用户仍可以手动修改这些值
- 提高添加账号的效率

### 3. 安全显示
- API Hash在设置页面只显示前8位字符，后面用"..."代替
- 保护敏感信息不被完全暴露

## 使用方法

### 设置默认API信息

1. **打开设置页面**：
   - 在主界面点击"设置"标签

2. **找到默认API设置**：
   - 在"应用设置"卡片中找到"默认API设置"部分

3. **设置API ID**：
   - 点击"默认API ID"行
   - 在弹出的对话框中输入您的API ID
   - 点击"保存"

4. **设置API Hash**：
   - 点击"默认API Hash"行
   - 在弹出的对话框中输入您的API Hash
   - 点击"保存"

### 添加账号时的体验

1. **打开添加账号对话框**：
   - 在设置页面的"账号管理"部分点击"添加账号"

2. **自动填入**：
   - API ID和API Hash字段会自动填入您设置的默认值
   - 您只需要输入账号名称和手机号码（或Bot Token）

3. **可选修改**：
   - 如果需要使用不同的API信息，可以手动修改这些字段

## 技术实现

### 前端修改

#### 1. AppProvider扩展 (frontend/lib/providers/app_provider.dart)

**新增属性**：
```dart
String _defaultApiId = '';
String _defaultApiHash = '';

String get defaultApiId => _defaultApiId;
String get defaultApiHash => _defaultApiHash;
```

**加载和保存设置**：
```dart
// 加载设置
_defaultApiId = prefs.getString('default_api_id') ?? '';
_defaultApiHash = prefs.getString('default_api_hash') ?? '';

// 保存设置
await prefs.setString('default_api_id', _defaultApiId);
await prefs.setString('default_api_hash', _defaultApiHash);
```

**设置方法**：
```dart
Future<void> setDefaultApiId(String value) async {
  _defaultApiId = value;
  await _saveSettings();
  notifyListeners();
}

Future<void> setDefaultApiHash(String value) async {
  _defaultApiHash = value;
  await _saveSettings();
  notifyListeners();
}
```

#### 2. 设置界面扩展 (frontend/lib/screens/main_screen.dart)

**设置页面新增部分**：
```dart
// 默认API设置
Text('默认API设置', style: Theme.of(context).textTheme.titleMedium),
Text('设置默认的API ID和API Hash，添加账号时会自动填入'),

ListTile(
  title: const Text('默认API ID'),
  subtitle: Text(appProvider.defaultApiId.isEmpty ? '未设置' : appProvider.defaultApiId),
  trailing: const Icon(Icons.edit),
  onTap: () => _showEditDefaultApiIdDialog(appProvider),
),

ListTile(
  title: const Text('默认API Hash'),
  subtitle: Text(appProvider.defaultApiHash.isEmpty ? '未设置' : 
    '${appProvider.defaultApiHash.substring(0, 8)}...'),
  trailing: const Icon(Icons.edit),
  onTap: () => _showEditDefaultApiHashDialog(appProvider),
),
```

**编辑对话框**：
```dart
Future<void> _showEditDefaultApiIdDialog(AppProvider appProvider) async {
  final controller = TextEditingController(text: appProvider.defaultApiId);
  
  final result = await showDialog<String>(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('设置默认API ID'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'API ID',
            border: OutlineInputBorder(),
            hintText: '例如: 1234567',
          ),
          keyboardType: TextInputType.number,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('保存'),
          ),
        ],
      );
    },
  );
  
  if (result != null) {
    await appProvider.setDefaultApiId(result);
  }
}
```

**添加账号对话框修改**：
```dart
Future<void> _showAddAccountDialog() async {
  final appProvider = Provider.of<AppProvider>(context, listen: false);
  
  final nameController = TextEditingController();
  final apiIdController = TextEditingController(text: appProvider.defaultApiId);
  final apiHashController = TextEditingController(text: appProvider.defaultApiHash);
  // ...
}
```

### 数据存储

**存储位置**：
- 使用SharedPreferences存储在本地
- 键名：`default_api_id` 和 `default_api_hash`

**数据持久性**：
- 设置会在应用重启后保持
- 与其他应用设置一起管理

## 用户体验改进

### 1. 便利性提升
- **减少重复输入**：大多数用户只有一套API凭据，设置一次后可重复使用
- **快速添加账号**：添加新账号时只需输入账号特定信息
- **减少错误**：避免每次手动输入时可能出现的拼写错误

### 2. 安全性考虑
- **本地存储**：API信息只存储在用户本地，不会上传到服务器
- **部分显示**：API Hash在界面上只显示部分内容，保护隐私
- **可修改性**：用户可以随时修改或清空默认设置

### 3. 灵活性保持
- **可选使用**：用户可以选择不设置默认值
- **可覆盖**：添加账号时仍可修改自动填入的值
- **独立设置**：每个账号仍可使用不同的API信息

## 使用场景

### 1. 个人用户
- 只有一套Telegram API凭据
- 需要管理多个Telegram账号
- 希望简化账号添加流程

### 2. 开发者/测试人员
- 频繁添加测试账号
- 使用相同的API凭据进行测试
- 需要快速配置多个账号

### 3. 团队使用
- 团队共享API凭据
- 需要为团队成员快速配置账号
- 标准化API配置流程

## 注意事项

### 1. API凭据获取
- 需要先在 https://my.telegram.org 申请API凭据
- API ID是数字，API Hash是32位字符串
- 每个应用都有唯一的API凭据

### 2. 安全建议
- 不要在公共场所设置API信息
- 定期检查和更新API凭据
- 如果怀疑泄露，及时在Telegram官网重新生成

### 3. 兼容性
- 功能向后兼容，不影响现有账号
- 未设置默认值时，添加账号流程保持不变
- 可以随时启用或禁用此功能

## 后续优化建议

### 1. 导入/导出功能
- 支持导出API设置到文件
- 支持从文件导入API设置
- 便于在多台设备间同步配置

### 2. 多套API支持
- 支持保存多套API凭据
- 添加账号时可选择使用哪套API
- 适用于有多个Telegram应用的用户

### 3. 验证功能
- 设置API信息时验证格式
- 检查API凭据的有效性
- 提供更好的错误提示

这个功能大大简化了添加账号的流程，特别是对于需要管理多个账号的用户来说，是一个非常实用的改进。
