#!/usr/bin/env python3
"""
测试跳过2FA功能的脚本
"""

import requests
import json
import sys

def test_skip_2fa_api():
    """测试跳过2FA的API功能"""
    base_url = "http://localhost:5000"
    
    print("测试跳过2FA功能...")
    
    # 测试数据
    test_data = {
        "phone_code": "12345",
        "skip_2fa": True
    }
    
    # 模拟验证码验证请求
    try:
        response = requests.post(
            f"{base_url}/api/accounts/1/verify_code",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"请求URL: {response.url}")
        print(f"请求数据: {json.dumps(test_data, indent=2)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到后端服务器，请确保后端正在运行")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
    
    return True

def test_normal_verify_code():
    """测试正常的验证码验证（不跳过2FA）"""
    base_url = "http://localhost:5000"
    
    print("\n测试正常验证码验证...")
    
    # 测试数据
    test_data = {
        "phone_code": "12345",
        "skip_2fa": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/accounts/1/verify_code",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"请求URL: {response.url}")
        print(f"请求数据: {json.dumps(test_data, indent=2)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到后端服务器，请确保后端正在运行")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
    
    return True

def test_api_compatibility():
    """测试API向后兼容性（不传skip_2fa参数）"""
    base_url = "http://localhost:5000"
    
    print("\n测试API向后兼容性...")
    
    # 测试数据（不包含skip_2fa参数）
    test_data = {
        "phone_code": "12345"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/accounts/1/verify_code",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"请求URL: {response.url}")
        print(f"请求数据: {json.dumps(test_data, indent=2)}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到后端服务器，请确保后端正在运行")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("跳过2FA功能测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("跳过2FA API测试", test_skip_2fa_api),
        ("正常验证码测试", test_normal_verify_code),
        ("API兼容性测试", test_api_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 失败: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    # 检查是否所有测试都通过
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n所有测试通过！")
        return 0
    else:
        print("\n部分测试失败，请检查实现。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
