# Session持久化问题修复

## 问题描述

用户反馈：打包成可执行文件后，退出应用再重新启动，登录状态就丢失了。

## 问题分析

### 原因

1. **Session文件路径问题**：
   - 开发环境：session文件保存在 `backend/sessions/` 目录
   - 打包后：session文件保存在可执行文件同级的 `sessions/` 目录
   - 当可执行文件位置改变时，session文件路径也会改变

2. **数据库路径问题**：
   - 数据库文件 `telegram_uploader.db` 使用相对路径保存
   - 打包后可能无法找到原来的数据库文件

3. **工作目录依赖**：
   - 应用依赖于当前工作目录来定位数据文件
   - 打包后工作目录可能不固定

### 数据存储位置

#### 后端数据
- **Session文件**：Pyrogram的session文件（.session格式）
- **数据库文件**：SQLite数据库（telegram_uploader.db）
- **存储内容**：
  - 账号信息（API ID、API Hash、手机号等）
  - Session字符串（登录凭证）
  - 上传任务历史
  - 文件夹历史记录
  - 群组历史记录

#### 前端数据
- **SharedPreferences**：Flutter的本地存储
- **存储内容**：
  - 选中的账号ID
  - 登录状态映射
  - 应用设置（自动刷新、刷新间隔等）
  - 当前文件夹路径

## 修复方案

### 1. 使用用户数据目录

修改应用使用标准的用户数据目录来存储持久化数据：

#### Windows
```
C:\Users\<USER>\AppData\Local\TelegramUploader\
```

#### macOS
```
~/Library/Application Support/TelegramUploader/
```

#### Linux
```
~/.local/share/TelegramUploader/
```

### 2. 目录结构

```
TelegramUploader/
├── telegram_uploader.db          # 主数据库
└── sessions/                     # Session文件目录
    ├── account_账号1.session
    ├── account_账号2.session
    └── ...
```

### 3. 代码修改

#### 后端修改 (backend/app.py)

1. **添加数据目录获取函数**：
```python
def get_app_data_dir():
    """获取应用程序数据目录"""
    app_name = "TelegramUploader"
    
    if sys.platform == "win32":
        # Windows: 使用 AppData/Local
        base_dir = os.environ.get('LOCALAPPDATA', os.path.expanduser('~'))
        return os.path.join(base_dir, app_name)
    elif sys.platform == "darwin":
        # macOS: 使用 Application Support
        return os.path.expanduser(f'~/Library/Application Support/{app_name}')
    else:
        # Linux: 使用 .local/share
        return os.path.expanduser(f'~/.local/share/{app_name}')
```

2. **初始化数据目录**：
```python
# 初始化应用数据目录
app_data_dir = get_app_data_dir()
os.makedirs(app_data_dir, exist_ok=True)

# 创建sessions目录
sessions_dir = os.path.join(app_data_dir, "sessions")
os.makedirs(sessions_dir, exist_ok=True)

# 数据库路径
db_path = os.path.join(app_data_dir, "telegram_uploader.db")
```

3. **传递路径给组件**：
```python
# 初始化组件
db = Database(db_path)
telegram_manager = TelegramClientManager(db, sessions_dir)
```

#### TelegramClientManager修改 (backend/telegram_client.py)

1. **接受sessions目录参数**：
```python
class TelegramClientManager:
    def __init__(self, db: Database, sessions_dir: str = "sessions"):
        self.db = db
        self.sessions_dir = sessions_dir
        # ...
```

2. **使用指定的sessions目录**：
```python
# 在create_client方法中
workdir=self.sessions_dir
```

### 4. 数据迁移

对于已有用户，可以考虑添加数据迁移逻辑：

1. **检查旧位置的数据**：
   - 检查当前目录下的 `telegram_uploader.db`
   - 检查当前目录下的 `sessions/` 文件夹

2. **自动迁移**：
   - 将旧数据复制到新的用户数据目录
   - 保留原文件作为备份

## 修复效果

### ✅ 解决的问题

1. **Session持久化**：
   - Session文件保存在固定的用户数据目录
   - 不受可执行文件位置影响
   - 重启应用后登录状态保持

2. **数据库持久化**：
   - 数据库文件保存在用户数据目录
   - 账号信息、历史记录等数据持久保存

3. **跨平台兼容**：
   - 自动适配不同操作系统的标准数据目录
   - 符合各平台的用户数据存储规范

4. **多用户支持**：
   - 每个用户有独立的数据目录
   - 不同用户的数据互不干扰

### 📁 数据位置示例

#### Windows用户
```
C:\Users\<USER>\AppData\Local\TelegramUploader\
├── telegram_uploader.db
└── sessions\
    ├── account_我的账号.session
    └── account_测试账号.session
```

#### macOS用户
```
/Users/<USER>/Library/Application Support/TelegramUploader/
├── telegram_uploader.db
└── sessions/
    ├── account_我的账号.session
    └── account_测试账号.session
```

## 注意事项

1. **权限问题**：
   - 确保应用有权限访问用户数据目录
   - 某些系统可能需要用户授权

2. **备份建议**：
   - 建议用户定期备份数据目录
   - 可以考虑添加导出/导入功能

3. **清理机制**：
   - 可以考虑添加清理旧session文件的功能
   - 删除无效或过期的登录数据

4. **调试信息**：
   - 启动时显示数据目录路径
   - 便于用户了解数据存储位置

## 测试建议

1. **新安装测试**：
   - 在全新环境中测试应用安装和首次运行
   - 验证数据目录正确创建

2. **重启测试**：
   - 登录账号后关闭应用
   - 重新启动验证登录状态保持

3. **迁移测试**：
   - 在有旧数据的环境中测试升级
   - 验证数据正确迁移到新位置

4. **跨平台测试**：
   - 在Windows、macOS、Linux上分别测试
   - 验证数据目录路径正确
