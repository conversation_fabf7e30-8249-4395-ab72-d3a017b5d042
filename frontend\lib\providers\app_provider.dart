import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';
import '../models/file_info.dart';
import '../models/task.dart';
import 'dart:async';

class AppProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  Timer? _statsTimer;
  Timer? _taskTimer;
  
  // 应用状态
  bool _isLoading = false;
  String? _error;
  UploadStats? _uploadStats;
  
  // 文件夹相关
  String _currentFolderPath = '';
  List<FileInfo> _scannedFiles = [];
  FolderStats? _folderStats;
  List<String> _folderHistory = [];
  
  // 设置
  bool _autoRefresh = true;
  int _refreshInterval = 2; // 秒
  bool _showNotifications = true;
  String _defaultApiId = '';
  String _defaultApiHash = '';
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  UploadStats? get uploadStats => _uploadStats;
  
  String get currentFolderPath => _currentFolderPath;
  List<FileInfo> get scannedFiles => _scannedFiles;
  FolderStats? get folderStats => _folderStats;
  List<String> get folderHistory => _folderHistory;
  
  bool get autoRefresh => _autoRefresh;
  int get refreshInterval => _refreshInterval;
  bool get showNotifications => _showNotifications;
  String get defaultApiId => _defaultApiId;
  String get defaultApiHash => _defaultApiHash;
  
  AppProvider() {
    _loadSettings();
    _startPeriodicUpdate();
  }
  
  @override
  void dispose() {
    _statsTimer?.cancel();
    _taskTimer?.cancel();
    super.dispose();
  }
  
  // 加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _autoRefresh = prefs.getBool('auto_refresh') ?? true;
      _refreshInterval = prefs.getInt('refresh_interval') ?? 2;
      _showNotifications = prefs.getBool('show_notifications') ?? true;
      _currentFolderPath = prefs.getString('current_folder_path') ?? '';
      _defaultApiId = prefs.getString('default_api_id') ?? '';
      _defaultApiHash = prefs.getString('default_api_hash') ?? '';

      notifyListeners();
    } catch (e) {
      _setError('加载设置失败: $e');
    }
  }
  
  // 保存设置
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_refresh', _autoRefresh);
      await prefs.setInt('refresh_interval', _refreshInterval);
      await prefs.setBool('show_notifications', _showNotifications);
      await prefs.setString('current_folder_path', _currentFolderPath);
      await prefs.setString('default_api_id', _defaultApiId);
      await prefs.setString('default_api_hash', _defaultApiHash);
    } catch (e) {
      _setError('保存设置失败: $e');
    }
  }
  
  // 开始定期更新
  void _startPeriodicUpdate() {
    if (_autoRefresh) {
      _statsTimer = Timer.periodic(Duration(seconds: _refreshInterval), (timer) {
        _updateStats();
      });
      
      _taskTimer = Timer.periodic(Duration(seconds: _refreshInterval * 2), (timer) {
        _updateTasks();
      });
    }
  }
  
  // 停止定期更新
  void _stopPeriodicUpdate() {
    _statsTimer?.cancel();
    _taskTimer?.cancel();
  }
  
  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // 设置错误
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  
  // 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  // 更新统计信息
  Future<void> _updateStats() async {
    try {
      final response = await _apiService.getStats();
      if (response.success) {
        _uploadStats = response.data;
        notifyListeners();
      }
    } catch (e) {
      // 静默处理错误，避免频繁显示错误
      if (kDebugMode) {
        print('更新统计信息失败: $e');
      }
    }
  }
  
  // 更新任务列表（由TaskProvider调用）
  Future<void> _updateTasks() async {
    // 这个方法由TaskProvider处理
  }
  
  // 扫描文件夹
  Future<void> scanFolder(String folderPath, {bool includeSubdirs = true}) async {
    if (folderPath.isEmpty) {
      _setError('文件夹路径不能为空');
      return;
    }
    
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.scanFolder(folderPath, includeSubdirs: includeSubdirs);
      
      if (response.success) {
        _scannedFiles = response.data!.files;
        _folderStats = response.data!.stats;
        _currentFolderPath = folderPath;
        
        // 更新历史记录
        await _updateFolderHistory();
        
        await _saveSettings();
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('扫描文件夹失败: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // 更新文件夹历史
  Future<void> _updateFolderHistory() async {
    try {
      final response = await _apiService.getFolderHistory();
      if (response.success) {
        _folderHistory = response.data ?? [];
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('更新文件夹历史失败: $e');
      }
    }
  }
  
  // 获取文件夹历史
  Future<void> loadFolderHistory() async {
    try {
      final response = await _apiService.getFolderHistory();
      if (response.success) {
        _folderHistory = response.data ?? [];
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('加载文件夹历史失败: $e');
    }
  }
  
  // 健康检查
  Future<bool> checkHealth() async {
    try {
      final response = await _apiService.healthCheck();
      return response.success;
    } catch (e) {
      _setError('后端服务连接失败: $e');
      return false;
    }
  }
  
  // 设置相关方法
  Future<void> setAutoRefresh(bool value) async {
    _autoRefresh = value;
    await _saveSettings();
    
    if (value) {
      _startPeriodicUpdate();
    } else {
      _stopPeriodicUpdate();
    }
    
    notifyListeners();
  }
  
  Future<void> setRefreshInterval(int seconds) async {
    _refreshInterval = seconds;
    await _saveSettings();
    
    if (_autoRefresh) {
      _stopPeriodicUpdate();
      _startPeriodicUpdate();
    }
    
    notifyListeners();
  }
  
  Future<void> setShowNotifications(bool value) async {
    _showNotifications = value;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setDefaultApiId(String value) async {
    _defaultApiId = value;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setDefaultApiHash(String value) async {
    _defaultApiHash = value;
    await _saveSettings();
    notifyListeners();
  }
  
  // 手动刷新
  Future<void> refresh() async {
    await _updateStats();
    if (_currentFolderPath.isNotEmpty) {
      await scanFolder(_currentFolderPath);
    }
  }
  
  // 搜索文件
  List<FileInfo> searchFiles(String query) {
    if (query.isEmpty) return _scannedFiles;
    
    final lowercaseQuery = query.toLowerCase();
    return _scannedFiles.where((file) {
      return file.name.toLowerCase().contains(lowercaseQuery) ||
             file.aPart.toLowerCase().contains(lowercaseQuery) ||
             file.bPart.toLowerCase().contains(lowercaseQuery) ||
             file.caption.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
  
  // 按类型筛选文件
  List<FileInfo> filterFilesByType(String type) {
    if (type == 'all') return _scannedFiles;
    return _scannedFiles.where((file) => file.type == type).toList();
  }
  
  // 获取文件统计
  Map<String, int> getFileTypeStats() {
    final stats = <String, int>{};
    for (final file in _scannedFiles) {
      stats[file.type] = (stats[file.type] ?? 0) + 1;
    }
    return stats;
  }
} 