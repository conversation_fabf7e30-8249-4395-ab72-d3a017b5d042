# 跳过2FA功能

## 概述

新增了跳过双因素认证(2FA)的功能，允许用户在登录时选择跳过2FA验证，适用于未启用2FA的账号或需要快速登录的场景。

## 功能特性

### 1. 验证码对话框中的跳过2FA选项
- 在输入验证码时，提供"跳过2FA验证"复选框
- 用户可以选择是否尝试跳过2FA验证
- 适用于未启用2FA的账号

### 2. 全局默认设置
- 在应用设置中添加"默认跳过2FA"开关
- 开启后，验证码对话框默认勾选跳过2FA选项
- 用户仍可在每次登录时手动调整

### 3. 智能处理逻辑
- 如果选择跳过2FA，系统首先尝试使用空密码验证
- 如果跳过失败，仍会提示用户输入2FA密码
- 保证了兼容性和安全性

## 使用方法

### 方法一：在验证码对话框中选择
1. 登录账号时，如果需要验证码，会弹出验证码输入对话框
2. 输入验证码
3. 勾选"跳过2FA验证"复选框（如果账号未启用2FA）
4. 点击"验证"按钮

### 方法二：设置全局默认行为
1. 在主界面点击"设置"标签
2. 在"应用设置"部分找到"默认跳过2FA"开关
3. 开启该开关
4. 之后所有登录时都会默认尝试跳过2FA

## 技术实现

### 后端更改

1. **TelegramClient类**
   - `login_with_code`方法新增`skip_2fa`参数
   - 在遇到`SessionPasswordNeeded`异常时，如果设置了跳过2FA，尝试使用空密码验证
   - 如果跳过失败，仍然要求用户输入密码

2. **API接口**
   - `/api/accounts/<id>/verify_code`接口新增`skip_2fa`参数
   - 支持传递跳过2FA的选项到后端处理

### 前端更改

1. **AppProvider**
   - 新增`defaultSkip2FA`设置项
   - 支持保存和加载跳过2FA的全局设置

2. **验证码对话框**
   - 使用`StatefulBuilder`支持动态状态更新
   - 添加跳过2FA的复选框
   - 使用全局设置作为默认值

3. **API服务**
   - `verifyCode`方法新增`skip2FA`参数
   - 支持传递跳过2FA选项到后端

## 安全考虑

1. **渐进式处理**
   - 只有在用户明确选择跳过2FA时才尝试
   - 如果跳过失败，仍然要求正常的2FA验证

2. **用户控制**
   - 用户可以在每次登录时选择是否跳过
   - 全局设置只是默认值，不强制执行

3. **兼容性**
   - 对于已启用2FA的账号，跳过操作会失败并回退到正常流程
   - 不影响现有的登录逻辑

## 适用场景

1. **未启用2FA的账号**
   - 可以直接跳过2FA验证步骤
   - 简化登录流程

2. **测试环境**
   - 快速登录测试账号
   - 减少手动输入步骤

3. **批量账号管理**
   - 对于大量未启用2FA的账号，可以快速登录

## 注意事项

1. **仅适用于未启用2FA的账号**
   - 如果账号已启用2FA，跳过操作会失败
   - 系统会自动回退到正常的2FA验证流程

2. **安全建议**
   - 建议在安全环境下使用跳过2FA功能
   - 对于重要账号，仍建议启用并使用2FA

3. **设置持久化**
   - 全局设置会保存在本地，重启应用后仍然有效
   - 用户可以随时修改设置
