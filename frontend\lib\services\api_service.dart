import 'package:dio/dio.dart';
import '../models/account.dart';
import '../models/file_info.dart';
import '../models/task.dart';
import '../models/proxy_config.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8090/api';
  late final Dio _dio;

  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;

  ApiService._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // 添加拦截器用于日志记录
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      error: true,
    ));
  }

  // 账号相关API
  Future<ApiResponse<List<Account>>> getAccounts() async {
    try {
      final response = await _dio.get('/accounts');
      if (response.data['success'] == true) {
        final List<dynamic> accountsData = response.data['accounts'];
        final accounts = accountsData.map((json) => Account.fromJson(json)).toList();
        return ApiResponse.success(accounts);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取账号失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<int>> addAccount({
    required String name,
    required int apiId,
    required String apiHash,
    String? phone,
    String? sessionString,
    String accountType = 'user',
    String? botToken,
  }) async {
    try {
      final response = await _dio.post('/accounts', data: {
        'name': name,
        'api_id': apiId,
        'api_hash': apiHash,
        'phone': phone,
        'session_string': sessionString,
        'account_type': accountType,
        'bot_token': botToken,
      });

      if (response.data['success'] == true) {
        return ApiResponse.success(response.data['account_id']);
      } else {
        return ApiResponse.error(response.data['error'] ?? '添加账号失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<void>> deleteAccount(int accountId) async {
    try {
      final response = await _dio.delete('/accounts/$accountId');
      
      if (response.data['success'] == true) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.data['error'] ?? '删除账号失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<LoginResult>> loginAccount(int accountId) async {
    try {
      final response = await _dio.post('/accounts/$accountId/login');
      
      if (response.data['success'] == true) {
        return ApiResponse.success(LoginResult(
          success: true,
          message: response.data['message'],
          needsCode: response.data['needs_code'] ?? false,
          needsPassword: response.data['needs_password'] ?? false,
        ));
      } else {
        return ApiResponse.error(response.data['error'] ?? '登录失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<bool>> verifyCode(int accountId, String phoneCode, {bool skip2FA = false}) async {
    try {
      final response = await _dio.post('/accounts/$accountId/verify_code', data: {
        'phone_code': phoneCode,
        'skip_2fa': skip2FA,
      });

      if (response.data['success'] == true) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['error'] ?? '验证码错误');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<bool>> verifyPassword(int accountId, String password) async {
    try {
      final response = await _dio.post('/accounts/$accountId/verify_password', data: {
        'password': password,
      });

      if (response.data['success'] == true) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['error'] ?? '密码错误');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<List<Dialog>>> getDialogs(int accountId) async {
    try {
      final response = await _dio.get('/accounts/$accountId/dialogs');
      
      if (response.data['success'] == true) {
        final List<dynamic> dialogsData = response.data['dialogs'];
        final dialogs = dialogsData.map((json) => Dialog.fromJson(json)).toList();
        return ApiResponse.success(dialogs);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取对话列表失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  // 文件夹相关API
  Future<ApiResponse<ScanResult>> scanFolder(String folderPath, {bool includeSubdirs = true}) async {
    try {
      final response = await _dio.post('/folders/scan', data: {
        'folder_path': folderPath,
        'include_subdirs': includeSubdirs,
      });

      if (response.data['success'] == true) {
        final List<dynamic> filesData = response.data['files'];
        final files = filesData.map((json) => FileInfo.fromJson(json)).toList();
        final stats = FolderStats.fromJson(response.data['stats']);
        
        return ApiResponse.success(ScanResult(files: files, stats: stats));
      } else {
        return ApiResponse.error(response.data['error'] ?? '扫描文件夹失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<List<String>>> getFolderHistory({int limit = 10}) async {
    try {
      final response = await _dio.get('/folders/history', queryParameters: {
        'limit': limit,
      });

      if (response.data['success'] == true) {
        final List<dynamic> foldersData = response.data['folders'];
        final folders = foldersData.cast<String>();
        return ApiResponse.success(folders);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取文件夹历史失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<List<Dialog>>> getGroupHistory(int accountId, {int limit = 10}) async {
    try {
      final response = await _dio.get('/groups/history/$accountId', queryParameters: {
        'limit': limit,
      });

      if (response.data['success'] == true) {
        final List<dynamic> groupsData = response.data['groups'];
        final groups = groupsData.map((json) => Dialog.fromJson(json)).toList();
        return ApiResponse.success(groups);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取群组历史失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<List<Dialog>>> getBotInputHistory(int accountId, {int limit = 10}) async {
    try {
      final response = await _dio.get('/bots/input-history/$accountId', queryParameters: {
        'limit': limit,
      });

      if (response.data['success'] == true) {
        final List<dynamic> inputsData = response.data['inputs'];
        final inputs = inputsData.map((json) => Dialog.fromJson(json)).toList();
        return ApiResponse.success(inputs);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取机器人输入历史失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  // 上传相关API
  Future<ApiResponse<int>> startUpload({
    required int accountId,
    required String folderPath,
    required String targetChatId,
    String? targetChatName,
  }) async {
    try {
      final response = await _dio.post('/upload', data: {
        'account_id': accountId,
        'folder_path': folderPath,
        'target_chat_id': targetChatId,
        'target_chat_name': targetChatName,
      });

      if (response.data['success'] == true) {
        return ApiResponse.success(response.data['task_id']);
      } else {
        return ApiResponse.error(response.data['error'] ?? '启动上传失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<List<UploadTask>>> getTasks() async {
    try {
      final response = await _dio.get('/tasks');
      
      if (response.data['success'] == true) {
        final List<dynamic> tasksData = response.data['tasks'];
        final tasks = tasksData.map((json) => UploadTask.fromJson(json)).toList();
        return ApiResponse.success(tasks);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取任务列表失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<List<UploadTask>>> getAllTasks({int limit = 100}) async {
    try {
      final response = await _dio.get('/tasks', queryParameters: {
        'include_history': 'true',
        'limit': limit,
      });
      
      if (response.data['success'] == true) {
        final List<dynamic> tasksData = response.data['tasks'];
        final tasks = tasksData.map((json) => UploadTask.fromJson(json)).toList();
        return ApiResponse.success(tasks);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取任务列表失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<void>> deleteTask(int taskId) async {
    try {
      final response = await _dio.delete('/tasks/$taskId');
      
      if (response.data['success'] == true) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.data['error'] ?? '删除任务失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<void>> pauseTask(int taskId) async {
    try {
      final response = await _dio.post('/tasks/$taskId/pause');
      
      if (response.data['success'] == true) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.data['error'] ?? '暂停任务失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<void>> resumeTask(int taskId) async {
    try {
      final response = await _dio.post('/tasks/$taskId/resume');
      
      if (response.data['success'] == true) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.data['error'] ?? '恢复任务失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<UploadTask>> getTaskDetail(int taskId) async {
    try {
      final response = await _dio.get('/tasks/$taskId');
      
      if (response.data['success'] == true) {
        final task = UploadTask.fromJson(response.data['task']);
        return ApiResponse.success(task);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取任务详情失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<UploadStats>> getStats() async {
    try {
      final response = await _dio.get('/stats');
      
      if (response.data['success'] == true) {
        final stats = UploadStats.fromJson(response.data['stats']);
        return ApiResponse.success(stats);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取统计信息失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<String>> healthCheck() async {
    try {
      final response = await _dio.get('/health');
      
      if (response.data['success'] == true) {
        return ApiResponse.success(response.data['message']);
      } else {
        return ApiResponse.error(response.data['error'] ?? '健康检查失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  // 代理配置相关API
  Future<ApiResponse<List<ProxyConfig>>> getProxyConfigs() async {
    try {
      final response = await _dio.get('/proxy');
      if (response.data['success'] == true) {
        final List<dynamic> proxiesData = response.data['proxies'];
        final proxies = proxiesData.map((json) => ProxyConfig.fromJson(json)).toList();
        return ApiResponse.success(proxies);
      } else {
        return ApiResponse.error(response.data['error'] ?? '获取代理配置失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<int>> addProxyConfig({
    required String name,
    required String proxyType,
    required String hostname,
    required int port,
    String? username,
    String? password,
    bool enabled = false,
    bool isDefault = false,
  }) async {
    try {
      final response = await _dio.post('/proxy', data: {
        'name': name,
        'proxy_type': proxyType,
        'hostname': hostname,
        'port': port,
        'username': username,
        'password': password,
        'enabled': enabled,
        'is_default': isDefault,
      });

      if (response.data['success'] == true) {
        return ApiResponse.success(response.data['proxy_id']);
      } else {
        return ApiResponse.error(response.data['error'] ?? '添加代理配置失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<bool>> updateProxyConfig(int proxyId, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/proxy/$proxyId', data: data);

      if (response.data['success'] == true) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['error'] ?? '更新代理配置失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<bool>> deleteProxyConfig(int proxyId) async {
    try {
      final response = await _dio.delete('/proxy/$proxyId');

      if (response.data['success'] == true) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.data['error'] ?? '删除代理配置失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }

  Future<ApiResponse<ProxyTestResult>> testProxyConfig(int proxyId) async {
    try {
      final response = await _dio.post('/proxy/test/$proxyId');

      if (response.data['success'] == true) {
        final result = ProxyTestResult.fromJson(response.data);
        return ApiResponse.success(result);
      } else {
        return ApiResponse.error(response.data['error'] ?? '测试代理配置失败');
      }
    } catch (e) {
      return ApiResponse.error('网络错误: $e');
    }
  }
}

// API响应包装类
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;

  ApiResponse.success(this.data) : success = true, error = null;
  ApiResponse.error(this.error) : success = false, data = null;
}

// 登录结果
class LoginResult {
  final bool success;
  final String message;
  final bool needsCode;
  final bool needsPassword;

  LoginResult({
    required this.success,
    required this.message,
    this.needsCode = false,
    this.needsPassword = false,
  });
}

// 扫描结果
class ScanResult {
  final List<FileInfo> files;
  final FolderStats stats;

  ScanResult({
    required this.files,
    required this.stats,
  });
} 