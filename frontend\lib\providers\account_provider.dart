import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';
import '../models/account.dart';
import 'dart:convert'; // Added for json.decode and json.encode

class AccountProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  // 状态
  bool _isLoading = false;
  String? _error;
  
  // 数据
  List<Account> _accounts = [];
  Account? _selectedAccount;
  List<Dialog> _dialogs = [];
  List<Dialog> _groupHistory = [];
  List<Dialog> _botInputHistory = [];
  
  // 登录状态
  Map<int, bool> _loginStatus = {};
  Map<int, String> _loginMessages = {};
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  List<Account> get accounts => _accounts;
  Account? get selectedAccount => _selectedAccount;
  List<Dialog> get dialogs => _dialogs;
  List<Dialog> get groupHistory => _groupHistory;
  List<Dialog> get botInputHistory => _botInputHistory;
  
  Map<int, bool> get loginStatus => _loginStatus;
  Map<int, String> get loginMessages => _loginMessages;
  
  // 获取已登录的账号
  List<Account> get loggedInAccounts => _accounts.where((account) => 
    _loginStatus[account.id] == true).toList();
  
  AccountProvider() {
    _loadSettings();
    loadAccounts();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedAccountId = prefs.getInt('selected_account_id');
      
      // 加载登录状态
      final loginStatusJson = prefs.getString('login_status');
      if (loginStatusJson != null) {
        final Map<String, dynamic> loginData = json.decode(loginStatusJson);
        _loginStatus = loginData.map((key, value) => MapEntry(int.parse(key), value as bool));
      }
      
      if (selectedAccountId != null && _accounts.isNotEmpty) {
        // 延迟设置选中的账号，等待账号列表加载完成
        Future.delayed(Duration.zero, () {
          final account = _accounts.firstWhere(
            (acc) => acc.id == selectedAccountId,
            orElse: () => _accounts.first,
          );
          setSelectedAccount(account);
        });
      }
    } catch (e) {
      _setError('加载设置失败: $e');
    }
  }

  // 保存设置
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_selectedAccount != null) {
        await prefs.setInt('selected_account_id', _selectedAccount!.id);
      }
      
      // 保存登录状态
      final loginStatusJson = json.encode(_loginStatus.map((key, value) => MapEntry(key.toString(), value)));
      await prefs.setString('login_status', loginStatusJson);
    } catch (e) {
      _setError('保存设置失败: $e');
    }
  }
  
  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // 设置错误
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  
  // 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  // 加载账号列表
  Future<void> loadAccounts() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.getAccounts();
      
      if (response.success) {
        _accounts = response.data ?? [];
        
        // 如果没有选中的账号，默认选中第一个
        if (_selectedAccount == null && _accounts.isNotEmpty) {
          _selectedAccount = _accounts.first;
          await _saveSettings();
        }
        
        // 自动登录有session的账号
        await _autoLoginAccounts();
        
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('加载账号列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  // 自动登录账号
  Future<void> _autoLoginAccounts() async {
    for (final account in _accounts) {
      // 如果账号有session字符串且之前登录过，尝试自动登录
      if (account.sessionString != null && 
          account.sessionString!.isNotEmpty &&
          _loginStatus[account.id] == true) {
        try {
          await _attemptAutoLogin(account);
        } catch (e) {
          if (kDebugMode) {
            print('账号 ${account.name} 自动登录失败: $e');
          }
          // 如果自动登录失败，清除登录状态
          _loginStatus[account.id] = false;
          _loginMessages[account.id] = '自动登录失败';
        }
      }
    }
    await _saveSettings();
  }

  // 尝试自动登录单个账号
  Future<void> _attemptAutoLogin(Account account) async {
    try {
      final response = await _apiService.loginAccount(account.id);
      
      if (response.success) {
        final loginResult = response.data!;
        
        if (!loginResult.needsCode && !loginResult.needsPassword) {
          _loginStatus[account.id] = true;
          _loginMessages[account.id] = '自动登录成功';
          
          // 加载对话列表
          await _loadDialogs(account.id);
          
          if (kDebugMode) {
            print('账号 ${account.name} 自动登录成功');
          }
        } else {
          // 需要验证码或密码，清除自动登录状态
          _loginStatus[account.id] = false;
          _loginMessages[account.id] = '需要重新验证';
        }
      } else {
        _loginStatus[account.id] = false;
        _loginMessages[account.id] = '自动登录失败';
      }
    } catch (e) {
      _loginStatus[account.id] = false;
      _loginMessages[account.id] = '自动登录失败: $e';
      rethrow;
    }
  }
  
  // 添加账号
  Future<bool> addAccount({
    required String name,
    required int apiId,
    required String apiHash,
    String? phone,
    String? sessionString,
    String accountType = 'user',
    String? botToken,
  }) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.addAccount(
        name: name,
        apiId: apiId,
        apiHash: apiHash,
        phone: phone,
        sessionString: sessionString,
        accountType: accountType,
        botToken: botToken,
      );
      
      if (response.success) {
        // 重新加载账号列表
        await loadAccounts();
        // 确保状态更新被通知
        notifyListeners();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('添加账号失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // 删除账号
  Future<bool> deleteAccount(int accountId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.deleteAccount(accountId);
      
      if (response.success) {
        // 如果删除的是当前选中的账号，清除选择
        if (_selectedAccount?.id == accountId) {
          _selectedAccount = null;
          await _saveSettings();
        }
        
        // 清除登录状态
        _loginStatus.remove(accountId);
        _loginMessages.remove(accountId);
        
        // 重新加载账号列表
        await loadAccounts();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('删除账号失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // 登录账号
  Future<bool> loginAccount(Account account) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.loginAccount(account.id);
      
      if (response.success) {
        final loginResult = response.data!;
        
        if (loginResult.needsCode) {
          _loginMessages[account.id] = '需要验证码';
          notifyListeners();
          return false; // 需要验证码
        } else if (loginResult.needsPassword) {
          _loginMessages[account.id] = '需要密码';
          notifyListeners();
          return false; // 需要密码
        } else {
          _loginStatus[account.id] = true;
          _loginMessages[account.id] = '登录成功';
          
          // 登录成功后获取对话列表
          await _loadDialogs(account.id);
          
          // 保存登录状态
          await _saveSettings();
          
          notifyListeners();
          return true;
        }
      } else {
        _setError(response.error);
        _loginStatus[account.id] = false;
        _loginMessages[account.id] = response.error ?? '登录失败';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _setError('登录失败: $e');
      _loginStatus[account.id] = false;
      _loginMessages[account.id] = '登录失败: $e';
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 验证验证码
  Future<String> verifyCode(int accountId, String phoneCode, {bool skip2FA = false}) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.verifyCode(accountId, phoneCode, skip2FA: skip2FA);

      if (response.success) {
        final data = response.data!;

        if (data['needs_password'] == true) {
          // 需要密码验证
          _loginMessages[accountId] = '需要密码';
          notifyListeners();
          return 'needs_password';
        } else {
          // 验证成功
          _loginStatus[accountId] = true;
          _loginMessages[accountId] = '验证成功';

          // 验证成功后获取对话列表
          await _loadDialogs(accountId);

          // 保存登录状态
          await _saveSettings();

          notifyListeners();
          return 'success';
        }
      } else {
        _setError(response.error);
        return 'error';
      }
    } catch (e) {
      _setError('验证失败: $e');
      return 'error';
    } finally {
      _setLoading(false);
    }
  }
  
  // 验证密码
  Future<bool> verifyPassword(int accountId, String password) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.verifyPassword(accountId, password);
      
      if (response.success) {
        _loginStatus[accountId] = true;
        _loginMessages[accountId] = '验证成功';
        
        // 验证成功后获取对话列表
        await _loadDialogs(accountId);
        
        // 保存登录状态
        await _saveSettings();
        
        notifyListeners();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('验证失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 设置选中的账号
  Future<void> setSelectedAccount(Account account) async {
    _selectedAccount = account;
    await _saveSettings();
    
    // 如果账号已登录，加载对话列表
    if (_loginStatus[account.id] == true) {
      await _loadDialogs(account.id);
    }
    
    // 加载群组历史
    await _loadGroupHistory(account.id);

    // 如果是机器人账号，加载输入历史
    if (account.accountType == 'bot') {
      await _loadBotInputHistory(account.id);
    }
    
    notifyListeners();
  }
  
  // 获取对话列表
  Future<void> _loadDialogs(int accountId) async {
    try {
      final response = await _apiService.getDialogs(accountId);
      
      if (response.success) {
        _dialogs = response.data ?? [];
        notifyListeners();
      } else {
        if (kDebugMode) {
          print('获取对话列表失败: ${response.error}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('获取对话列表失败: $e');
      }
    }
  }
  
  // 加载群组历史
  Future<void> _loadGroupHistory(int accountId) async {
    try {
      final response = await _apiService.getGroupHistory(accountId);

      if (response.success) {
        _groupHistory = response.data ?? [];
        notifyListeners();
      } else {
        if (kDebugMode) {
          print('获取群组历史失败: ${response.error}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('获取群组历史失败: $e');
      }
    }
  }

  // 加载机器人输入历史
  Future<void> _loadBotInputHistory(int accountId) async {
    try {
      final response = await _apiService.getBotInputHistory(accountId);

      if (response.success) {
        _botInputHistory = response.data ?? [];
        notifyListeners();
      } else {
        if (kDebugMode) {
          print('获取机器人输入历史失败: ${response.error}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('获取机器人输入历史失败: $e');
      }
    }
  }
  
  // 刷新对话列表
  Future<void> refreshDialogs() async {
    if (_selectedAccount != null && _loginStatus[_selectedAccount!.id] == true) {
      await _loadDialogs(_selectedAccount!.id);
    }
  }
  
  // 刷新群组历史
  Future<void> refreshGroupHistory() async {
    if (_selectedAccount != null) {
      await _loadGroupHistory(_selectedAccount!.id);
    }
  }

  // 刷新机器人输入历史
  Future<void> refreshBotInputHistory() async {
    if (_selectedAccount != null && _selectedAccount!.accountType == 'bot') {
      await _loadBotInputHistory(_selectedAccount!.id);
    }
  }
  
  // 获取账号的登录状态
  bool isAccountLoggedIn(int accountId) {
    return _loginStatus[accountId] == true;
  }
  
  // 获取账号的登录消息
  String getAccountMessage(int accountId) {
    return _loginMessages[accountId] ?? '';
  }
  
  // 根据类型筛选对话
  List<Dialog> getDialogsByType(String type) {
    if (type == 'all') return _dialogs;
    return _dialogs.where((dialog) => dialog.type == type).toList();
  }
  
  // 搜索对话
  List<Dialog> searchDialogs(String query) {
    if (query.isEmpty) return _dialogs;
    
    final lowercaseQuery = query.toLowerCase();
    return _dialogs.where((dialog) {
      return dialog.title.toLowerCase().contains(lowercaseQuery) ||
             (dialog.username?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }
  
  // 获取对话统计
  Map<String, int> getDialogStats() {
    final stats = <String, int>{};
    for (final dialog in _dialogs) {
      stats[dialog.type] = (stats[dialog.type] ?? 0) + 1;
    }
    return stats;
  }
} 