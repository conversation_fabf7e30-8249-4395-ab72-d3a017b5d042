# 群组选择器搜索功能

## 功能概述

为群组选择器对话框的"搜索"标签页添加了关键字搜索功能，用户可以通过输入关键字快速找到目标群组/频道。

## 功能特性

### 1. 实时搜索
- 输入关键字时实时过滤结果
- 支持搜索群组/频道名称、ID和用户名
- 不区分大小写的模糊匹配

### 2. 搜索高亮
- 搜索结果中的匹配关键字会高亮显示
- 使用黄色背景和粗体字突出显示匹配部分
- 提升搜索结果的可读性

### 3. 用户友好的界面
- 清晰的搜索框设计，带有搜索图标
- 输入内容时显示清除按钮
- 无搜索结果时显示友好的提示信息

### 4. 搜索状态管理
- 搜索框状态与过滤结果实时同步
- 清除搜索时恢复完整列表
- 保持搜索体验的流畅性

## 技术实现

### 1. 状态管理扩展

**新增状态变量**：
```dart
class _GroupSelectorDialogState extends State<_GroupSelectorDialog>
    with SingleTickerProviderStateMixin {
  // 原有状态...
  String _searchKeyword = '';                              // 搜索关键字
  final TextEditingController _searchController = TextEditingController(); // 搜索框控制器
}
```

**资源清理**：
```dart
@override
void dispose() {
  _tabController.dispose();
  _searchController.dispose();  // 清理搜索控制器
  super.dispose();
}
```

### 2. 搜索过滤逻辑

**过滤算法**：
```dart
final filteredDialogs = widget.accountProvider.dialogs.where((dialog) {
  if (_searchKeyword.isEmpty) return true;
  
  final keyword = _searchKeyword.toLowerCase();
  final title = dialog.title.toLowerCase();
  final id = dialog.id.toLowerCase();
  final username = dialog.username?.toLowerCase() ?? '';
  
  return title.contains(keyword) || 
         id.contains(keyword) || 
         username.contains(keyword);
}).toList();
```

**搜索范围**：
- **群组/频道名称**：主要搜索目标
- **ID**：支持通过ID精确查找
- **用户名**：支持@username格式搜索

### 3. 搜索界面设计

**搜索框设计**：
```dart
TextField(
  controller: _searchController,
  decoration: InputDecoration(
    hintText: '搜索群组/频道名称、ID或用户名...',
    prefixIcon: const Icon(Icons.search),
    suffixIcon: _searchKeyword.isNotEmpty
        ? IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchKeyword = '';
              });
            },
          )
        : null,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  ),
  onChanged: (value) {
    setState(() {
      _searchKeyword = value;
    });
  },
)
```

**界面布局**：
```dart
Column(
  children: [
    // 搜索框
    Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(...),
    ),
    
    // 搜索结果
    Expanded(
      child: _buildSearchResults(),
    ),
  ],
)
```

### 4. 高亮显示实现

**高亮文本组件**：
```dart
Widget _buildHighlightedText(String text, String keyword) {
  if (keyword.isEmpty) {
    return Text(text);
  }
  
  final lowerText = text.toLowerCase();
  final lowerKeyword = keyword.toLowerCase();
  final index = lowerText.indexOf(lowerKeyword);
  
  if (index == -1) {
    return Text(text);
  }
  
  return RichText(
    text: TextSpan(
      style: DefaultTextStyle.of(context).style,
      children: [
        if (index > 0)
          TextSpan(text: text.substring(0, index)),
        TextSpan(
          text: text.substring(index, index + keyword.length),
          style: const TextStyle(
            backgroundColor: Colors.yellow,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (index + keyword.length < text.length)
          TextSpan(text: text.substring(index + keyword.length)),
      ],
    ),
  );
}
```

### 5. 搜索结果状态处理

**多种状态展示**：
```dart
// 无数据状态
if (widget.accountProvider.dialogs.isEmpty)
  const Center(child: Text('暂无对话数据'))

// 无搜索结果状态  
else if (filteredDialogs.isEmpty && _searchKeyword.isNotEmpty)
  Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.search_off, size: 48, color: Colors.grey),
        const SizedBox(height: 16),
        Text(
          '未找到包含 "$_searchKeyword" 的结果',
          style: const TextStyle(color: Colors.grey),
        ),
      ],
    ),
  )

// 正常搜索结果
else
  ListView.builder(...)
```

## 用户体验优化

### 1. 搜索交互优化
- **实时反馈**：输入时立即显示过滤结果
- **清除功能**：一键清除搜索内容
- **视觉提示**：搜索图标和清除按钮提供明确的操作指引

### 2. 搜索结果优化
- **高亮匹配**：匹配的关键字用黄色背景突出显示
- **无结果提示**：友好的无搜索结果提示，包含搜索图标和说明文字
- **保持布局**：搜索时保持列表项的一致布局

### 3. 性能优化
- **客户端过滤**：在已加载的数据中进行过滤，响应速度快
- **状态同步**：搜索状态与UI实时同步，无延迟感
- **内存管理**：正确清理控制器资源，避免内存泄漏

## 使用场景

### 1. 快速查找
- **大量群组**：当用户加入了很多群组时，通过搜索快速定位
- **模糊记忆**：只记得群组名称的部分内容时进行模糊搜索
- **ID查找**：通过群组ID进行精确查找

### 2. 提升效率
- **减少滚动**：避免在长列表中滚动查找
- **精确匹配**：通过关键字快速缩小选择范围
- **批量操作**：频繁选择群组时提高操作效率

### 3. 用户体验
- **降低认知负担**：不需要记住群组在列表中的位置
- **提供即时反馈**：输入时立即看到搜索结果
- **错误容忍**：支持模糊匹配，容忍拼写差异

## 搜索功能特点

### 1. 搜索范围全面
```dart
// 支持多字段搜索
return title.contains(keyword) ||     // 群组名称
       id.contains(keyword) ||        // 群组ID  
       username.contains(keyword);    // 用户名
```

### 2. 不区分大小写
```dart
// 统一转换为小写进行比较
final keyword = _searchKeyword.toLowerCase();
final title = dialog.title.toLowerCase();
```

### 3. 实时过滤
```dart
// 输入时立即触发过滤
onChanged: (value) {
  setState(() {
    _searchKeyword = value;
  });
}
```

### 4. 视觉反馈丰富
- 搜索图标提示搜索功能
- 清除按钮便于重置搜索
- 高亮显示匹配结果
- 无结果时显示友好提示

## 后续优化建议

### 1. 搜索历史
```dart
// 可以添加搜索历史功能
List<String> _searchHistory = [];

void _addToSearchHistory(String keyword) {
  if (keyword.isNotEmpty && !_searchHistory.contains(keyword)) {
    _searchHistory.insert(0, keyword);
    if (_searchHistory.length > 10) {
      _searchHistory.removeLast();
    }
  }
}
```

### 2. 搜索建议
```dart
// 基于群组名称提供搜索建议
List<String> _getSearchSuggestions(String input) {
  return widget.accountProvider.dialogs
      .map((dialog) => dialog.title)
      .where((title) => title.toLowerCase().startsWith(input.toLowerCase()))
      .take(5)
      .toList();
}
```

### 3. 高级搜索
```dart
// 支持搜索类型过滤
enum SearchType { all, groups, channels, private }

Widget _buildSearchTypeFilter() {
  return SegmentedButton<SearchType>(
    segments: const [
      ButtonSegment(value: SearchType.all, label: Text('全部')),
      ButtonSegment(value: SearchType.groups, label: Text('群组')),
      ButtonSegment(value: SearchType.channels, label: Text('频道')),
    ],
    selected: {_selectedSearchType},
    onSelectionChanged: (Set<SearchType> selection) {
      setState(() {
        _selectedSearchType = selection.first;
      });
    },
  );
}
```

### 4. 搜索性能优化
```dart
// 使用防抖避免频繁搜索
Timer? _searchDebounce;

void _onSearchChanged(String value) {
  if (_searchDebounce?.isActive ?? false) _searchDebounce!.cancel();
  _searchDebounce = Timer(const Duration(milliseconds: 300), () {
    setState(() {
      _searchKeyword = value;
    });
  });
}
```

## 总结

这个搜索功能通过以下方式显著提升了群组选择的用户体验：

1. **快速定位**：通过关键字搜索快速找到目标群组
2. **实时反馈**：输入时立即显示过滤结果
3. **视觉优化**：高亮显示匹配内容，提供清晰的视觉反馈
4. **用户友好**：无搜索结果时显示友好提示，支持一键清除

这个功能特别适合管理大量群组的用户，能够显著提高群组选择的效率和准确性。
